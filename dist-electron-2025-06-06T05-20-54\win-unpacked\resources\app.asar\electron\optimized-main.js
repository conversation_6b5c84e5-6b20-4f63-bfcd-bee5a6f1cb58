const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');
const fs = require('fs');
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

// Keep a global reference of the window object
let mainWindow;
let expressApp;
let server;

const isDev = process.env.NODE_ENV === 'development';
const PORT = process.env.PORT || 3000;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    icon: path.join(__dirname, '../public/vite.svg'),
    show: false // Don't show until ready
  });

  // Hide the menu bar
  Menu.setApplicationMenu(null);

  // Load the app
  if (isDev) {
    // Development mode - load from Vite dev server
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production mode - load from Express server
    mainWindow.loadURL(`http://localhost:${PORT}`);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });
}

function startEmbeddedServer() {
  return new Promise((resolve, reject) => {
    if (isDev) {
      // In development, assume server is started separately
      resolve();
      return;
    }

    try {
      // Create Express app
      expressApp = express();

      // Set up database path for production
      const dbDir = path.join(app.getPath('userData'), 'database');
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      const dbPath = path.join(dbDir, 'shop.db');

      // Set environment variables
      process.env.DB_PATH = dbPath;
      process.env.NODE_ENV = 'production';

      // Initialize database
      const { initDatabase } = require('./db-init');

      initDatabase(dbPath).then(() => {
        // Set up Express middleware
        expressApp.use(cors());
        expressApp.use(bodyParser.json());
        expressApp.use(bodyParser.urlencoded({ extended: true }));

        // Serve static files
        expressApp.use(express.static(path.join(__dirname, '../dist')));

        // Import routes - use temp-server path for optimized build
        const serverPath = fs.existsSync(path.join(__dirname, '../temp-server')) 
          ? '../temp-server' 
          : '../server';

        const authRoutes = require(`${serverPath}/routes/auth`);
        const productsRoutes = require(`${serverPath}/routes/products`);
        const categoriesRoutes = require(`${serverPath}/routes/categories`);
        const ordersRoutes = require(`${serverPath}/routes/orders`);
        const reportsRoutes = require(`${serverPath}/routes/reports`);

        // Import middleware
        const { verifyToken } = require(`${serverPath}/middleware/auth`);
        const { requestLogger } = require(`${serverPath}/middleware/logger`);

        // Request logging middleware - log all API requests
        expressApp.use('/api', requestLogger);

        // Authentication routes - no authentication required
        expressApp.use('/api/auth', authRoutes);

        // API routes requiring authentication
        expressApp.use('/api/products', verifyToken);
        expressApp.use('/api/products', productsRoutes);

        expressApp.use('/api/categories', verifyToken);
        expressApp.use('/api/categories', categoriesRoutes);

        expressApp.use('/api/orders', verifyToken);
        expressApp.use('/api/orders', ordersRoutes);

        expressApp.use('/api/reports', verifyToken);
        expressApp.use('/api/reports', reportsRoutes);

        // Serve frontend for all other routes
        expressApp.get('*', (req, res) => {
          res.sendFile(path.join(__dirname, '../dist/index.html'));
        });

        // Start server
        server = expressApp.listen(PORT, () => {
          console.log(`内嵌服务器运行在 http://localhost:${PORT}`);
          resolve();
        });

        server.on('error', (err) => {
          console.error('Server error:', err);
          reject(err);
        });

      }).catch(reject);

    } catch (err) {
      console.error('Failed to start embedded server:', err);
      reject(err);
    }
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  try {
    await startEmbeddedServer();
    createWindow();
  } catch (error) {
    console.error('Failed to start application:', error);
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // Close server if it exists
  if (server) {
    server.close();
  }

  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Close server when app is quitting
  if (server) {
    server.close();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    require('electron').shell.openExternal(navigationUrl);
  });
});
