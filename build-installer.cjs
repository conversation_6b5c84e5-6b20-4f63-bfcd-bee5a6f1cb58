const { build } = require('electron-builder');
const path = require('path');
const fs = require('fs');

async function buildInstaller() {
  try {
    console.log('🚀 开始构建 Windows 安装程序...\n');
    
    // 确保前端已构建
    console.log('1. 检查前端构建...');
    const distPath = path.join(__dirname, 'dist');
    if (!fs.existsSync(distPath)) {
      console.log('   前端未构建，正在构建...');
      const { execSync } = require('child_process');
      execSync('pnpm run build', { stdio: 'inherit' });
    }
    console.log('   ✅ 前端构建完成');

    // 构建配置
    const config = {
      appId: 'com.shop.desktop',
      productName: 'Shop Desktop',
      directories: {
        output: 'dist-installer'
      },
      asar: true,
      files: [
        'dist/**/*',
        'electron/**/*',
        'server/**/*',
        '!server/node_modules',
        '!server/logs/**/*',
        '!server/test/**/*',
        '!**/*.map',
        '!**/*.md',
        '!**/README*',
        '!**/CHANGELOG*',
        '!**/LICENSE*',
        '!**/.git*',
        '!**/test/**/*',
        '!**/tests/**/*',
        '!**/__tests__/**/*',
        '!**/spec/**/*',
        '!**/*.spec.*',
        '!**/*.test.*',
        '!**/coverage/**/*',
        '!**/docs/**/*',
        '!**/examples/**/*',
        '!**/demo/**/*',
        '!**/.nyc_output/**/*',
        '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}',
        '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}',
        '!**/node_modules/*.d.ts',
        '!**/node_modules/.bin',
        '!**/node_modules/*/man/**/*',
        '!**/node_modules/*/docs/**/*',
        '!**/node_modules/*/example/**/*',
        '!**/node_modules/*/examples/**/*',
        '!**/node_modules/*/test/**/*',
        '!**/node_modules/*/tests/**/*',
        '!**/node_modules/*/*.md'
      ],
      extraResources: [
        {
          from: 'server/db/schema.sql',
          to: 'server/db/schema.sql'
        },
        {
          from: 'server/db/initial-data.sql',
          to: 'server/db/initial-data.sql'
        }
      ],
      win: {
        target: [
          {
            target: 'nsis',
            arch: ['x64']
          },
          {
            target: 'portable',
            arch: ['x64']
          }
        ],
        icon: 'public/vite.svg',
        verifyUpdateCodeSignature: false
      },
      nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true,
        allowElevation: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true,
        menuCategory: 'Business',
        shortcutName: 'Shop Desktop',
        uninstallDisplayName: 'Shop Desktop',
        installerIcon: 'public/vite.svg',
        uninstallerIcon: 'public/vite.svg',
        installerHeaderIcon: 'public/vite.svg',
        differentialPackage: false,
        perMachine: false,
        runAfterFinish: true,
        deleteAppDataOnUninstall: false,
        include: 'installer.nsh',
        artifactName: '${productName}-${version}-Setup.${ext}'
      },
      portable: {
        artifactName: '${productName}-${version}-Portable.${ext}'
      },
      nodeGypRebuild: false,
      buildDependenciesFromSource: false,
      compression: 'maximum',
      npmRebuild: false
    };

    console.log('2. 开始构建安装程序...');
    
    // 执行构建
    const result = await build({
      config,
      win: ['nsis', 'portable'],
      publish: 'never'
    });

    console.log('\n✅ 构建完成！');
    
    // 显示构建结果
    const outputDir = path.resolve('dist-installer');
    console.log(`\n📦 安装程序位置: ${outputDir}`);
    
    if (fs.existsSync(outputDir)) {
      const files = fs.readdirSync(outputDir);
      console.log('\n📁 生成的文件:');
      files.forEach(file => {
        const filePath = path.join(outputDir, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`   ${file} (${sizeInMB} MB)`);
      });
    }
    
    console.log('\n🎯 安装程序类型说明:');
    console.log('   📦 Setup.exe - 标准安装程序，需要安装到系统');
    console.log('   💾 Portable.exe - 便携版，无需安装，可直接运行');
    
    console.log('\n🚀 分发说明:');
    console.log('   1. Setup.exe - 适合最终用户安装使用');
    console.log('   2. Portable.exe - 适合临时使用或U盘携带');
    console.log('   3. 两个版本都包含完整功能');
    
    return result;
  } catch (error) {
    console.error('❌ 构建失败:', error);
    throw error;
  }
}

// 运行构建
if (require.main === module) {
  buildInstaller()
    .then(() => {
      console.log('\n🎉 Windows 安装程序构建成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 构建失败:', error.message);
      process.exit(1);
    });
}

module.exports = buildInstaller;
