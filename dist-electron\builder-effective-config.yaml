directories:
  output: dist-electron
  buildResources: build
appId: com.shop.desktop
productName: Shop Desktop
electronDownload:
  cache: ./electron-cache
  mirror: file://
asar: true
files:
  - filter:
      - dist/**/*
      - electron/**/*
      - server/**/*
      - '!server/node_modules'
      - '!server/logs/**/*'
      - '!server/test/**/*'
      - '!**/*.map'
      - '!**/*.md'
      - '!**/README*'
      - '!**/CHANGELOG*'
      - '!**/LICENSE*'
      - '!**/.git*'
      - '!**/test/**/*'
      - '!**/tests/**/*'
      - '!**/__tests__/**/*'
      - '!**/spec/**/*'
      - '!**/*.spec.*'
      - '!**/*.test.*'
      - '!**/coverage/**/*'
      - '!**/docs/**/*'
      - '!**/examples/**/*'
      - '!**/demo/**/*'
      - '!**/.nyc_output/**/*'
      - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
      - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
      - '!**/node_modules/*.d.ts'
      - '!**/node_modules/.bin'
      - '!**/node_modules/*/man/**/*'
      - '!**/node_modules/*/docs/**/*'
      - '!**/node_modules/*/example/**/*'
      - '!**/node_modules/*/examples/**/*'
      - '!**/node_modules/*/test/**/*'
      - '!**/node_modules/*/tests/**/*'
      - '!**/node_modules/*/*.md'
extraResources:
  - from: server/db/schema.sql
    to: server/db/schema.sql
  - from: server/db/initial-data.sql
    to: server/db/initial-data.sql
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: portable
      arch:
        - x64
  icon: public/vite.svg
  verifyUpdateCodeSignature: false
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  allowElevation: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  menuCategory: Business
  shortcutName: Shop Desktop
  uninstallDisplayName: Shop Desktop
  installerIcon: public/vite.svg
  uninstallerIcon: public/vite.svg
  installerHeaderIcon: public/vite.svg
  differentialPackage: false
  perMachine: false
  runAfterFinish: true
  deleteAppDataOnUninstall: false
  include: installer.nsh
portable:
  artifactName: ${productName}-${version}-portable.${ext}
nodeGypRebuild: false
buildDependenciesFromSource: false
compression: maximum
npmRebuild: false
electronVersion: 36.4.0
