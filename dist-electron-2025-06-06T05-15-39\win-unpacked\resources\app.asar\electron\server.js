const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');
const { app: electronApp } = require('electron');

// Import routes
const authRoutes = require('../server/routes/auth');
const productsRoutes = require('../server/routes/products');
const categoriesRoutes = require('../server/routes/categories');
const ordersRoutes = require('../server/routes/orders');
const reportsRoutes = require('../server/routes/reports');

// Import middleware
const { verifyToken, isAdmin, isCashierOrAdmin } = require('../server/middleware/auth');
const { requestLogger } = require('../server/middleware/logger');

// Import database initialization
const { initDatabase, getDb } = require('./db-init');

const app = express();
const PORT = process.env.PORT || 3000;

// Database setup for Electron
const dbPath = process.env.DB_PATH || path.join(electronApp.getPath('userData'), 'database', 'shop.db');

// Export database functions for use in routes
module.exports.getDb = getDb;

// Set up logging
const logDir = path.join(electronApp.getPath('userData'), 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

const accessLogStream = fs.createWriteStream(
  path.join(logDir, 'access.log'),
  { flags: 'a' }
);

// Redirect console logs to file
const originalConsoleLog = console.log;
console.log = function() {
  const isApiLog = arguments[0] && 
    typeof arguments[0] === 'string' && 
    /^(GET|POST|PUT|DELETE|PATCH).*\[\d{3}\]/.test(arguments[0]);
  
  if (isApiLog) {
    const timestamp = new Date().toISOString();
    accessLogStream.write(`[${timestamp}] ${arguments[0]}\n`);
  }

  originalConsoleLog.apply(console, arguments);
};

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Request logging middleware
app.use('/api', requestLogger);

// Static files service
app.use(express.static(path.join(__dirname, '../dist')));

// Authentication routes - no authentication required
app.use('/api/auth', authRoutes);

// API routes requiring authentication
app.use('/api/products', verifyToken);
app.use('/api/products', productsRoutes);

app.use('/api/categories', verifyToken);
app.use('/api/categories', categoriesRoutes);

app.use('/api/orders', verifyToken);
app.use('/api/orders', ordersRoutes);

app.use('/api/reports', verifyToken);
app.use('/api/reports', reportsRoutes);

// All other GET requests return frontend app
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// Initialize database and start server
initDatabase(dbPath)
  .then(() => {
    app.listen(PORT, () => {
      console.log(`服务器运行在 http://localhost:${PORT}`);
    });
  })
  .catch((err) => {
    console.error('Database initialization failed:', err);
    process.exit(1);
  });
