const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const express = require('express');
const cors = require('cors');

// Keep a global reference of the window object
let mainWindow;
let serverProcess;
let expressApp;
let server;

const isDev = process.env.NODE_ENV === 'development';
const PORT = process.env.PORT || 3000;

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: true
    },
    show: false // Don't show until ready
  });

  // Hide the menu bar
  Menu.setApplicationMenu(null);

  // Load the app
  if (isDev) {
    // Development mode - load from Vite dev server
    mainWindow.loadURL('http://localhost:5173');
    // Open DevTools in development
    mainWindow.webContents.openDevTools();
  } else {
    // Production mode - load from Express server
    mainWindow.loadURL(`http://localhost:${PORT}`);
  }

  // Show window when ready to prevent visual flash
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Emitted when the window is closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url);
    return { action: 'deny' };
  });
}

function startServer() {
  return new Promise((resolve, reject) => {
    if (isDev) {
      // In development, assume server is started separately
      resolve();
      return;
    }

    try {
      // Create Express app
      expressApp = express();

      // Set up database path for production
      const dbDir = path.join(app.getPath('userData'), 'database');
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      const dbPath = path.join(dbDir, 'shop.db');

      // Set environment variables
      process.env.DB_PATH = dbPath;
      process.env.NODE_ENV = 'production';

      // Initialize database
      const { initDatabase, getDb } = require('./db-init.cjs');

      initDatabase(dbPath).then(() => {
        // Set up Express middleware
        expressApp.use(cors());
        expressApp.use(express.json());
        expressApp.use(express.urlencoded({ extended: true }));

        // Serve static files
        expressApp.use(express.static(path.join(__dirname, '../dist')));

        // Make database available globally for routes
        global.getDb = getDb;

        // Import routes
        const authRoutes = require('../server/routes/auth');
        const productsRoutes = require('../server/routes/products');
        const categoriesRoutes = require('../server/routes/categories');
        const ordersRoutes = require('../server/routes/orders');
        const reportsRoutes = require('../server/routes/reports');

        // Import middleware
        const { verifyToken, isAdmin, isCashierOrAdmin } = require('../server/middleware/auth');
        const { requestLogger } = require('../server/middleware/logger');

        // Request logging middleware - log all API requests
        expressApp.use('/api', requestLogger);

        // Authentication routes - no authentication required
        expressApp.use('/api/auth', authRoutes);

        // API routes requiring authentication
        // Product management - admin can CRUD, everyone can view
        expressApp.use('/api/products', verifyToken);
        expressApp.use('/api/products', productsRoutes);

        // Category management - admin can CRUD, everyone can view
        expressApp.use('/api/categories', verifyToken);
        expressApp.use('/api/categories', categoriesRoutes);

        // Order management - cashiers and admins can operate
        expressApp.use('/api/orders', verifyToken);
        expressApp.use('/api/orders', ordersRoutes);

        // Report management - only admins can access
        expressApp.use('/api/reports', verifyToken);
        expressApp.use('/api/reports', reportsRoutes);

        // Serve frontend for all other routes
        expressApp.get('*', (req, res) => {
          res.sendFile(path.join(__dirname, '../dist/index.html'));
        });

        // Start server
        server = expressApp.listen(PORT, () => {
          console.log(`服务器运行在 http://localhost:${PORT}`);
          resolve();
        });

        server.on('error', (err) => {
          console.error('Server error:', err);
          reject(err);
        });

      }).catch(reject);

    } catch (err) {
      console.error('Failed to start server:', err);
      reject(err);
    }
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  try {
    await startServer();
    createWindow();
  } catch (error) {
    console.error('Failed to start application:', error);
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

// Quit when all windows are closed
app.on('window-all-closed', () => {
  // Close server if it exists
  if (server) {
    server.close();
  }
  if (serverProcess) {
    serverProcess.kill();
  }

  // On macOS, keep app running even when all windows are closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('before-quit', () => {
  // Close server when app is quitting
  if (server) {
    server.close();
  }
  if (serverProcess) {
    serverProcess.kill();
  }
});

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    require('electron').shell.openExternal(navigationUrl);
  });
});
