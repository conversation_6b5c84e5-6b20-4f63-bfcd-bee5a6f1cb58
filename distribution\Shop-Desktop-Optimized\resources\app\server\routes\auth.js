const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const { verifyToken } = require('../middleware/auth');

// Get database connection - works in both standalone and Electron environments
function getDb() {
  if (global.getDb) {
    // Electron environment
    return global.getDb();
  } else {
    // Standalone server environment
    const { getDb: localGetDb } = require('../db/init');
    return localGetDb();
  }
}

// JWT密钥，实际项目中应放在环境变量
const JWT_SECRET = 'your-jwt-secret-key';

// 登录认证
router.post('/login', (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }
    
    const db = getDb();
    
    // 查询用户
    const user = db.prepare(
      'SELECT id, username, role FROM users WHERE username = ? AND password = ?'
    ).get(username, password);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        token,
        user: {
          id: user.id,
          username: user.username,
          role: user.role
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

// 获取当前用户信息
router.get('/me', verifyToken, (req, res) => {
  res.json({
    success: true,
    data: {
      id: req.user.id,
      username: req.user.username,
      role: req.user.role
    }
  });
});

module.exports = router; 