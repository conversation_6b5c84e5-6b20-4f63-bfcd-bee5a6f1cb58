const express = require('express');
const router = express.Router();
const { isAdmin } = require('../middleware/auth');

// Get database connection - works in both standalone and Electron environments
function getDb() {
  if (global.getDb) {
    // Electron environment
    return global.getDb();
  } else {
    // Standalone server environment
    const { getDb: localGetDb } = require('../db/init');
    return localGetDb();
  }
}

// 获取所有品类
router.get('/', (req, res) => {
  try {
    const db = getDb();
    
    const categories = db.prepare(`
      SELECT * FROM categories ORDER BY name
    `).all();
    
    // 格式化品类数据
    const formattedCategories = categories.map(category => ({
      id: category.id,
      name: category.name,
      description: category.description,
      createdAt: category.created_at,
      updatedAt: category.updated_at
    }));
    
    res.json({
      success: true,
      data: formattedCategories
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取品类失败'
    });
  }
});

// 获取单个品类
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const db = getDb();
    
    const category = db.prepare(`
      SELECT * FROM categories WHERE id = ?
    `).get(id);
    
    if (!category) {
      return res.status(404).json({
        success: false,
        message: '品类不存在'
      });
    }
    
    // 格式化品类数据
    const formattedCategory = {
      id: category.id,
      name: category.name,
      description: category.description,
      createdAt: category.created_at,
      updatedAt: category.updated_at
    };
    
    res.json({
      success: true,
      data: formattedCategory
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取品类详情失败'
    });
  }
});

// 创建品类 - 仅管理员
router.post('/', isAdmin, (req, res) => {
  try {
    const { name, description } = req.body;
    
    // 验证必填字段
    if (!name) {
      return res.status(400).json({
        success: false,
        message: '品类名称不能为空'
      });
    }
    
    const db = getDb();
    
    // 检查名称是否已存在
    const existing = db.prepare('SELECT id FROM categories WHERE name = ?').get(name);
    
    if (existing) {
      return res.status(409).json({
        success: false,
        message: '该品类名称已存在'
      });
    }
    
    // 插入品类
    const result = db.prepare(`
      INSERT INTO categories (name, description)
      VALUES (?, ?)
    `).run(name, description || null);
    
    const categoryId = result.lastInsertRowid;
    
    // 获取新创建的品类
    const newCategory = db.prepare('SELECT * FROM categories WHERE id = ?').get(categoryId);
    
    // 格式化品类数据
    const formattedCategory = {
      id: newCategory.id,
      name: newCategory.name,
      description: newCategory.description,
      createdAt: newCategory.created_at,
      updatedAt: newCategory.updated_at
    };
    
    res.status(201).json({
      success: true,
      data: formattedCategory
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建品类失败'
    });
  }
});

// 更新品类 - 仅管理员
router.put('/:id', isAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;
    
    // 验证必填字段
    if (!name) {
      return res.status(400).json({
        success: false,
        message: '品类名称不能为空'
      });
    }
    
    const db = getDb();
    
    // 检查品类是否存在
    const existingCategory = db.prepare('SELECT id FROM categories WHERE id = ?').get(id);
    
    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        message: '品类不存在'
      });
    }
    
    // 检查名称是否已被其他品类使用
    const nameExists = db.prepare('SELECT id FROM categories WHERE name = ? AND id != ?').get(name, id);
    
    if (nameExists) {
      return res.status(409).json({
        success: false,
        message: '该品类名称已被使用'
      });
    }
    
    // 更新品类
    db.prepare(`
      UPDATE categories
      SET name = ?, description = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `).run(name, description || null, id);
    
    // 获取更新后的品类
    const updatedCategory = db.prepare('SELECT * FROM categories WHERE id = ?').get(id);
    
    // 格式化品类数据
    const formattedCategory = {
      id: updatedCategory.id,
      name: updatedCategory.name,
      description: updatedCategory.description,
      createdAt: updatedCategory.created_at,
      updatedAt: updatedCategory.updated_at
    };
    
    res.json({
      success: true,
      data: formattedCategory
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新品类失败'
    });
  }
});

// 删除品类 - 仅管理员
router.delete('/:id', isAdmin, (req, res) => {
  try {
    const { id } = req.params;
    const db = getDb();
    
    // 检查品类是否存在
    const existingCategory = db.prepare('SELECT id FROM categories WHERE id = ?').get(id);
    
    if (!existingCategory) {
      return res.status(404).json({
        success: false,
        message: '品类不存在'
      });
    }
    
    // 检查是否有商品关联到该品类
    const products = db.prepare('SELECT COUNT(*) as count FROM products WHERE category_id = ?').get(id);
    
    if (products.count > 0) {
      return res.status(409).json({
        success: false,
        message: '无法删除使用中的品类，请先移除或更改关联商品的品类'
      });
    }
    
    // 删除品类
    db.prepare('DELETE FROM categories WHERE id = ?').run(id);
    
    res.json({
      success: true,
      message: '品类删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除品类失败'
    });
  }
});

module.exports = router; 