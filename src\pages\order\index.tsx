import { useEffect, useState } from "react";
import { Plus, Minus, ShoppingCart, Trash2, Tag, Printer } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { useProductsStore } from "@/store/products";
import { useCartStore } from "@/store/cart";
import { useOrdersStore, COMMON_REMARKS } from "@/store/orders";
import { formatCurrency } from "@/lib/utils";
import type { Product, ProductOption } from "@/types";
import { usePrinterStore } from "@/store/printer";

export default function OrderPage() {
  // 商品和分类相关状态
  const { products, categories, fetchProducts, fetchCategories, isLoading, fetchProductsByCategory } = useProductsStore();
  
  // 购物车相关状态
  const { items: cartItems, addItem, removeItem, clearCart, totalAmount } = useCartStore();
  
  // 订单相关状态
  const { createOrder } = useOrdersStore();
  
  // 打印机相关状态
  const { printers, printOrder } = usePrinterStore();
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [selectedPrinter, setSelectedPrinter] = useState<string>("");
  
  // 对话框状态
  const [checkoutDialogOpen, setCheckoutDialogOpen] = useState(false);
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<"cash" | "wechat" | "alipay" | "card">("cash");
  const [remark, setRemark] = useState("");
  
  // 选中的商品和规格
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedOption, setSelectedOption] = useState<ProductOption | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [itemRemark, setItemRemark] = useState("");
  
  // 初始化加载数据
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [fetchProducts, fetchCategories]);
  
  // 处理选择商品
  const handleSelectProduct = (product: Product) => {
    setSelectedProduct(product);
    // 默认选择第一个规格或默认规格
    const defaultOption = product.options.find((opt) => opt.isDefault) || product.options[0];
    setSelectedOption(defaultOption);
    setQuantity(1);
    setItemRemark(""); // 重置商品备注
    // 打开商品选择对话框
    setProductDialogOpen(true);
  };
  
  // 处理选择规格
  const handleSelectOption = (optionId: number) => {
    if (!selectedProduct) return;
    const option = selectedProduct.options.find((opt) => opt.id === optionId);
    if (option) {
      setSelectedOption(option);
    }
  };
  
  // 处理添加备注标签
  const handleAddRemarkTag = (tag: string) => {
    // 如果标签已经存在，则不重复添加
    if (itemRemark.includes(tag)) return;
    
    // 如果已有其他内容，添加逗号分隔
    const newRemark = itemRemark ? `${itemRemark}, ${tag}` : tag;
    setItemRemark(newRemark);
  };
  
  // 处理添加到购物车
  const handleAddToCart = () => {
    if (!selectedProduct || !selectedOption) return;
    
    addItem({
      productId: selectedProduct.id,
      name: selectedProduct.name,
      optionId: selectedOption.id,
      optionName: selectedOption.name,
      price: selectedOption.price,
      quantity: quantity,
      remark: itemRemark.trim() || undefined
    });
    
    toast.success("已添加到购物车");
    
    // 重置选择并关闭对话框
    setProductDialogOpen(false);
    setSelectedProduct(null);
    setSelectedOption(null);
    setQuantity(1);
    setItemRemark("");
  };
  
  // 处理取消选择
  const handleCancelSelection = () => {
    setProductDialogOpen(false);
    setSelectedProduct(null);
    setSelectedOption(null);
    setQuantity(1);
    setItemRemark("");
  };
  
  // 处理结账
  const handleCheckout = async () => {
    if (cartItems.length === 0) {
      toast.error("购物车为空");
      return;
    }
    
    setCheckoutDialogOpen(true);
  };
  
  // 处理打印订单
  const handlePrintOrder = async () => {
    try {
      if (!selectedPrinter) {
        toast.error("请选择打印机");
        return;
      }
      
      // 准备打印内容
      let content = "======= 订单小票 =======\n\n";
      content += `订单时间: ${new Date().toLocaleString()}\n`;
      content += `支付方式: ${
        paymentMethod === "cash" ? "现金" : "线上支付"
      }\n`;
      content += "----------------------\n";
      
      // 添加商品列表
      cartItems.forEach((item) => {
        content += `${item.name} (${item.optionName})\n`;
        content += `  x${item.quantity}  ${formatCurrency(item.price * item.quantity)}\n`;
        if (item.remark) {
          content += `  备注: ${item.remark}\n`;
        }
      });
      
      content += "----------------------\n";
      content += `总计: ${formatCurrency(totalAmount)}\n`;
      
      if (remark) {
        content += `备注: ${remark}\n`;
      }
      
      content += "\n======= 谢谢惠顾 =======";
      
      // 发送打印任务
      await printOrder(selectedPrinter, content);
      setPrintDialogOpen(false);
      setSelectedPrinter("");
      
      toast.success("订单已发送至打印机");
    } catch (error) {
      console.error("打印订单失败:", error);
      toast.error("打印订单失败");
    }
  };
  
  // 处理支付
  const handlePay = async () => {
    try {
      // 创建订单
      const orderItems = cartItems.map((item) => ({
        productId: item.productId,
        productName: item.name,
        optionId: item.optionId,
        optionName: item.optionName,
        price: item.price,
        quantity: item.quantity,
        remark: item.remark
      }));
      
      // 重新计算订单总金额，确保准确
      const calculatedTotal = cartItems.reduce(
        (sum, item) => sum + item.price * item.quantity, 
        0
      );
      
      const order = await createOrder({
        items: orderItems as any, // 类型转换
        totalAmount: calculatedTotal, // 使用计算的总金额
        paymentMethod,
        status: "completed",
        remark,
        cashierId: 1, // 硬编码，实际应该从登录用户获取
        cashierName: "收银员1", // 硬编码，实际应该从登录用户获取
      });
      
      // 清空购物车
      clearCart();
      
      // 关闭对话框
      setCheckoutDialogOpen(false);
      
      // 提示成功
      toast.success(`订单 ${order.orderNumber} 创建成功`);
      
      // 如果有打印机，提示是否打印
      if (printers.length > 0) {
        setPrintDialogOpen(true);
      }
      
      // 重置状态
      setPaymentMethod("cash");
      setRemark("");
    } catch (error: any) {
      console.error("创建订单失败:", error);
      
      // 处理不可用商品的错误
      if (error.response && error.response.data && error.response.data.invalidProducts) {
        const invalidProducts = error.response.data.invalidProducts;
        
        // 显示具体的不可用商品
        const invalidProductNames = invalidProducts.map((p: any) => `${p.name}(${p.reason})`).join(', ');
        toast.error(`无法创建订单: ${invalidProductNames}`);
        
        // 刷新商品列表，确保显示最新状态
        fetchProducts();
      } else {
        toast.error("创建订单失败");
      }
    }
  };

  return (
    <div className="space-y-3">
      <div className="grid grid-cols-1 gap-3 lg:grid-cols-4">
        {/* 商品列表区域 */}
        <div className="lg:col-span-3">
          <Card className="h-full">
            <CardHeader className="pb-2 pt-3 px-3">
              <CardTitle className="text-base">商品列表</CardTitle>
            </CardHeader>
            <CardContent className="p-3">
              {/* 分类选项卡 */}
              <Tabs defaultValue="all" className="mb-3">
                <TabsList className="w-full overflow-auto h-9">
                  <TabsTrigger value="all" onClick={() => fetchProducts()} className="h-7 text-xs">
                    全部
                  </TabsTrigger>
                  {categories.map((category) => (
                    <TabsTrigger
                      key={category.id}
                      value={`category-${category.id}`}
                      onClick={() => fetchProductsByCategory(category.id)}
                      className="h-7 text-xs"
                    >
                      {category.name}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
              
              {/* 商品网格 */}
              {isLoading ? (
                <div className="flex h-64 items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
                </div>
              ) : products.length === 0 ? (
                <div className="flex h-64 items-center justify-center">
                  <p className="text-muted-foreground">暂无商品</p>
                </div>
              ) : (
                <div className="grid grid-cols-3 gap-2 sm:grid-cols-4 md:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 2xl:grid-cols-10">
                  {products.map((product) => (
                    <Card
                      key={product.id}
                      className="cursor-pointer transition-all hover:shadow-md"
                      onClick={() => handleSelectProduct(product)}
                    >
                      <CardContent className="p-2">
                        <div className="aspect-square w-full overflow-hidden rounded-md bg-muted">
                          {/* 实际项目中这里应该显示商品图片 */}
                          <div className="flex h-full items-center justify-center bg-muted text-muted-foreground text-xs">
                            {product.name.charAt(0)}
                          </div>
                        </div>
                        <div className="mt-1 text-center">
                          <h3 className="font-medium truncate">{product.name}</h3>
                          <p className="text-xs text-muted-foreground">
                            {formatCurrency(product.options[0].price)}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 购物车区域 */}
        <div>
          <Card className="h-full">
            <CardHeader className="pb-2 pt-3 px-3">
              <CardTitle className="flex items-center justify-between text-base">
                <span>购物车</span>
                <ShoppingCart className="h-4 w-4" />
              </CardTitle>
            </CardHeader>
            <CardContent className="p-3 space-y-3">
              {cartItems.length === 0 ? (
                <div className="flex h-32 flex-col items-center justify-center rounded-lg border border-dashed p-3 text-center text-muted-foreground">
                  <ShoppingCart className="mb-1 h-8 w-8" />
                  <p className="text-xs">购物车为空</p>
                  <p className="text-xs">点击商品添加到购物车</p>
                </div>
              ) : (
                <div className="space-y-2 max-h-[400px] overflow-y-auto pr-1">
                  {cartItems.map((item) => (
                    <div
                      key={item.id}
                      className="flex items-center justify-between rounded-lg border p-2"
                    >
                      <div className="w-3/5 overflow-hidden">
                        <h5 className="font-medium text-xs truncate">{item.name}</h5>
                        <p className="text-xs text-muted-foreground truncate">
                          {item.optionName} x {item.quantity}
                        </p>
                        {item.remark && (
                          <div className="mt-1 flex items-center">
                            <Tag className="h-3 w-3 mr-1 text-muted-foreground" />
                            <p className="text-xs text-muted-foreground truncate">{item.remark}</p>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-1">
                        <span className="text-xs whitespace-nowrap">{formatCurrency(item.price * item.quantity)}</span>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6"
                          onClick={() => removeItem(item.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            {cartItems.length > 0 && (
              <CardFooter className="flex flex-col space-y-3 p-3">
                <div className="flex w-full items-center justify-between border-t pt-3">
                  <span className="text-sm font-medium">总计：</span>
                  <span className="text-base font-bold text-primary">
                    {formatCurrency(totalAmount)}
                  </span>
                </div>
                <Button
                  className="w-full"
                  size="sm"
                  onClick={handleCheckout}
                >
                  结算
                </Button>
              </CardFooter>
            )}
          </Card>
        </div>
      </div>

      {/* 商品选择对话框 */}
      <Dialog open={productDialogOpen} onOpenChange={setProductDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{selectedProduct?.name}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-2">
            <div className="space-y-2">
              <Label htmlFor="option" className="text-sm">规格</Label>
              <Select
                value={selectedOption?.id.toString()}
                onValueChange={(value) => handleSelectOption(Number(value))}
              >
                <SelectTrigger id="option">
                  <SelectValue placeholder="选择规格" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {selectedProduct?.options.map((option) => (
                      <SelectItem key={option.id} value={option.id.toString()}>
                        {option.name} ({formatCurrency(option.price)})
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quantity" className="text-sm">数量</Label>
              <div className="flex items-center">
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                >
                  <Minus className="h-4 w-4" />
                </Button>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={quantity}
                  onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                  className="mx-2 w-16 h-8 text-center"
                />
                <Button
                  variant="outline"
                  size="icon"
                  className="h-8 w-8"
                  onClick={() => setQuantity(quantity + 1)}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="remark" className="text-sm">备注</Label>
              <div className="mb-2 flex flex-wrap gap-1">
                {COMMON_REMARKS.map((tag) => (
                  <Badge 
                    key={tag} 
                    variant="outline" 
                    className="cursor-pointer hover:bg-secondary"
                    onClick={() => handleAddRemarkTag(tag)}
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
              <Textarea
                id="remark"
                value={itemRemark}
                onChange={(e) => setItemRemark(e.target.value)}
                placeholder="选择或输入特殊要求"
                className="resize-none h-20"
              />
            </div>
            
            {selectedOption && (
              <div>
                <p className="flex justify-between text-sm">
                  <span>小计：</span>
                  <span className="font-medium">
                    {formatCurrency(selectedOption.price * quantity)}
                  </span>
                </p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCancelSelection}>
              取消
            </Button>
            <Button onClick={handleAddToCart}>加入购物车</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* 结账对话框 */}
      <Dialog open={checkoutDialogOpen} onOpenChange={setCheckoutDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>订单结算</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid w-full max-w-sm items-center gap-2">
              <Label htmlFor="payment-method" className="text-sm">支付方式</Label>
              <Select
                value={paymentMethod}
                onValueChange={(value) => setPaymentMethod(value as any)}
              >
                <SelectTrigger id="payment-method" className="h-9">
                  <SelectValue placeholder="选择支付方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">现金</SelectItem>
                  <SelectItem value="wechat">线上支付</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid w-full max-w-sm items-center gap-2">
              <Label htmlFor="remark" className="text-sm">订单备注</Label>
              <Input
                id="remark"
                value={remark}
                onChange={(e) => setRemark(e.target.value)}
                placeholder="可选"
                className="h-9"
              />
            </div>
            <div className="rounded-lg bg-muted p-3">
              <h4 className="font-medium text-sm">订单明细</h4>
              <div className="mt-2 space-y-1 text-xs max-h-[200px] overflow-y-auto pr-1">
                {cartItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex flex-col py-1"
                  >
                    <div className="flex justify-between">
                      <span>
                        {item.name} ({item.optionName}) x {item.quantity}
                      </span>
                      <span>{formatCurrency(item.price * item.quantity)}</span>
                    </div>
                    {item.remark && (
                      <div className="flex items-center text-muted-foreground mt-0.5">
                        <Tag className="h-3 w-3 mr-1" />
                        <span>{item.remark}</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="mt-2 flex justify-between border-t pt-2 text-sm font-medium">
                <span>总计：</span>
                <span>{formatCurrency(totalAmount)}</span>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCheckoutDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handlePay}>确认付款</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* 打印订单对话框 */}
      <Dialog open={printDialogOpen} onOpenChange={setPrintDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>打印订单</DialogTitle>
            <DialogDescription>
              选择要使用的打印机打印订单小票。
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid w-full max-w-sm items-center gap-2">
              <Label htmlFor="printer">选择打印机</Label>
              <Select
                value={selectedPrinter}
                onValueChange={setSelectedPrinter}
              >
                <SelectTrigger id="printer">
                  <SelectValue placeholder="选择打印机" />
                </SelectTrigger>
                <SelectContent>
                  {printers.map((printer) => (
                    <SelectItem key={printer.sn} value={printer.sn}>
                      {printer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setPrintDialogOpen(false)}>
              取消
            </Button>
            <Button onClick={handlePrintOrder}>
              <Printer className="mr-2 h-4 w-4" />
              打印
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 