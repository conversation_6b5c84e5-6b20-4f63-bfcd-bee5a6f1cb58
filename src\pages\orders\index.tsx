import { useEffect, useState } from "react";
import { Eye, ArrowLeft, ArrowRight, ShoppingBag, Trash2 } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent,
  DialogDescription,
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { useOrdersStore } from "@/store/orders";
import { useAuthStore } from "@/store/auth";
import { formatDate, formatCurrency } from "@/lib/utils";
import type { Order } from "@/types";

export default function OrdersPage() {
  const { orders, pagination, isLoading, fetchOrders, updateOrderStatus, deleteOrder } = useOrdersStore();
  const { user } = useAuthStore();
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [orderDialogOpen, setOrderDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [orderToDelete, setOrderToDelete] = useState<Order | null>(null);
  const [currentPage, setCurrentPage] = useState(1);

  // 是否是管理员
  const isAdmin = user?.role === 'admin';

  // 初始加载数据
  useEffect(() => {
    fetchOrders(currentPage);
  }, [fetchOrders, currentPage]);

  // 查看订单详情
  const handleViewOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setOrderDialogOpen(true);
  };

  // 处理更新订单状态
  const handleUpdateStatus = async (id: number, newStatus: Order["status"]) => {
    try {
      await updateOrderStatus(id, newStatus);
      toast.success("订单状态已更新");
      
      if (selectedOrder && selectedOrder.id === id) {
        setSelectedOrder(prev => prev ? {...prev, status: newStatus} : null);
      }
    } catch (error) {
      console.error("更新订单状态失败:", error);
      toast.error("更新订单状态失败");
    }
  };

  // 处理删除订单
  const handleDeleteOrder = async () => {
    if (!orderToDelete) return;
    
    try {
      await deleteOrder(orderToDelete.id);
      setDeleteDialogOpen(false);
      setOrderToDelete(null);
      
      // 如果删除的是当前查看的订单，关闭详情对话框
      if (selectedOrder && selectedOrder.id === orderToDelete.id) {
        setOrderDialogOpen(false);
        setSelectedOrder(null);
      }
    } catch (error) {
      console.error("删除订单失败:", error);
    }
  };

  // 打开删除确认对话框
  const openDeleteDialog = (order: Order) => {
    setOrderToDelete(order);
    setDeleteDialogOpen(true);
  };

  // 获取订单状态的中文名称
  const getStatusText = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return "待处理";
      case "completed":
        return "已完成";
      case "cancelled":
        return "已取消";
      default:
        return status;
    }
  };

  // 获取支付方式的中文名称
  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case "cash":
        return "现金";
      case "wechat":
        return "线上支付";
      default:
        return method;
    }
  };

  // 分页处理
  const totalPages = Math.ceil(pagination.total / pagination.pageSize);

  const handlePreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2 px-4 sm:px-0">
        <h2 className="text-2xl sm:text-3xl font-bold tracking-tight">订单管理</h2>
        <p className="text-muted-foreground text-sm sm:text-base">
          查看和管理所有订单记录
        </p>
      </div>

      <Card className="mx-4 sm:mx-0">
        <CardHeader>
          <CardTitle>订单列表</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto mb-4"></div>
              <p>正在加载订单数据...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12 space-y-4">
              <div className="bg-gray-100 rounded-full p-4 w-16 h-16 mx-auto flex items-center justify-center">
                <ShoppingBag className="text-gray-500 w-8 h-8" />
              </div>
              <h3 className="text-lg font-medium">暂无订单数据</h3>
              <p className="text-muted-foreground text-sm">
                当前没有可显示的订单记录
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <Table className="min-w-[800px]">
                  <TableHeader>
                    <TableRow>
                      <TableHead>订单编号</TableHead>
                      <TableHead>创建时间</TableHead>
                      <TableHead>金额</TableHead>
                      <TableHead>支付方式</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>收银员</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {orders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell className="font-medium">{order.orderNumber}</TableCell>
                        <TableCell>{formatDate(order.createdAt, "yyyy-MM-dd HH:mm")}</TableCell>
                        <TableCell>{formatCurrency(order.totalAmount)}</TableCell>
                        <TableCell>{getPaymentMethodText(order.paymentMethod)}</TableCell>
                        <TableCell>
                          <span className={`rounded-full px-2 py-1 text-xs font-medium ${
                            order.status === "completed" ? "bg-green-100 text-green-800" :
                            order.status === "cancelled" ? "bg-red-100 text-red-800" :
                            "bg-yellow-100 text-yellow-800"
                          }`}>
                            {getStatusText(order.status)}
                          </span>
                        </TableCell>
                        <TableCell>{order.cashierName}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewOrderDetails(order)}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              查看
                            </Button>
                            
                            {isAdmin && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openDeleteDialog(order)}
                                className="text-destructive hover:text-destructive hover:bg-destructive/10"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {!isLoading && pagination.total > pagination.pageSize && (
                <div className="flex flex-col sm:flex-row items-center justify-between px-2 mt-4 gap-4">
                  <div className="text-sm text-muted-foreground">
                    显示 {(currentPage - 1) * pagination.pageSize + 1} 到{" "}
                    {Math.min(currentPage * pagination.pageSize, pagination.total)} 条，
                    共 {pagination.total} 条
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePreviousPage}
                      disabled={currentPage <= 1}
                    >
                      <ArrowLeft className="h-4 w-4 mr-1" />
                      上一页
                    </Button>
                    
                    <div className="px-3 py-1 text-sm bg-muted rounded-md">
                      {currentPage} / {totalPages}
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNextPage}
                      disabled={currentPage >= totalPages}
                    >
                      下一页
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* 订单详情对话框 */}
      <Dialog open={orderDialogOpen} onOpenChange={setOrderDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>订单详情</DialogTitle>
            <DialogDescription>订单详细信息</DialogDescription>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">订单编号</p>
                  <p className="font-medium">{selectedOrder.orderNumber}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">创建时间</p>
                  <p className="font-medium">{formatDate(selectedOrder.createdAt, "yyyy-MM-dd HH:mm")}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">支付方式</p>
                  <p className="font-medium">{getPaymentMethodText(selectedOrder.paymentMethod)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">当前状态</p>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className={`${
                      selectedOrder.status === "completed" ? "bg-green-100 text-green-800" :
                      selectedOrder.status === "cancelled" ? "bg-red-100 text-red-800" :
                      "bg-yellow-100 text-yellow-800 border-none"
                    }`}>
                      {getStatusText(selectedOrder.status)}
                    </Badge>
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant={selectedOrder.status === "completed" ? "default" : "outline"}
                        disabled={selectedOrder.status === "completed"}
                        onClick={() => handleUpdateStatus(selectedOrder.id, "completed")}
                      >
                        标记为完成
                      </Button>
                      <Button
                        size="sm"
                        variant={selectedOrder.status === "cancelled" ? "destructive" : "outline"}
                        disabled={selectedOrder.status === "cancelled"}
                        onClick={() => handleUpdateStatus(selectedOrder.id, "cancelled")}
                      >
                        取消订单
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-2">订单商品</h4>
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>商品</TableHead>
                        <TableHead>规格</TableHead>
                        <TableHead>单价</TableHead>
                        <TableHead>数量</TableHead>
                        <TableHead className="text-right">小计</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedOrder.items.map((item) => (
                        <TableRow key={item.id}>
                          <TableCell>{item.product_name}</TableCell>
                          <TableCell>{item.option_name}</TableCell>
                          <TableCell>{formatCurrency(item.price)}</TableCell>
                          <TableCell>{item.quantity}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.price * item.quantity)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>

                <div className="flex justify-end mt-4">
                  <div className="w-48">
                    <div className="flex justify-between border-t pt-2">
                      <span className="font-medium">订单总计：</span>
                      <span className="font-bold text-lg">{formatCurrency(selectedOrder.totalAmount)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {selectedOrder.remark && (
                <div>
                  <h4 className="font-medium mb-1">备注</h4>
                  <p className="text-muted-foreground">{selectedOrder.remark}</p>
                </div>
              )}
              
              {/* 管理员删除订单按钮 */}
              {isAdmin && (
                <div className="pt-4 border-t flex justify-end">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      setOrderDialogOpen(false);
                      openDeleteDialog(selectedOrder);
                    }}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    删除订单
                  </Button>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
      
      {/* 删除订单确认对话框 */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除订单</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除订单 {orderToDelete?.orderNumber} 吗？此操作无法撤销，订单及其所有相关信息将被永久删除。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteOrder} >
              确认删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 