const { build } = require('electron-builder');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

async function buildFresh() {
  try {
    console.log('🚀 开始全新Electron构建（使用本地压缩包）...\n');
    
    // 1. 构建前端
    console.log('🔨 构建React前端...');
    execSync('pnpm run build', { stdio: 'inherit' });
    console.log('   ✅ 前端构建完成');

    // 2. 检查并准备本地Electron压缩包
    console.log('📦 准备本地Electron压缩包...');
    const electronZipPath = path.join(__dirname, 'electron-v36.4.0-win32-x64.zip');
    if (!fs.existsSync(electronZipPath)) {
      throw new Error(`本地Electron压缩包不存在: ${electronZipPath}`);
    }
    
    // 确保electron-cache目录存在
    const electronCacheDir = path.join(__dirname, 'electron-cache');
    if (!fs.existsSync(electronCacheDir)) {
      fs.mkdirSync(electronCacheDir, { recursive: true });
    }
    
    // 清理不完整的下载文件
    const existingCacheFiles = fs.readdirSync(electronCacheDir);
    existingCacheFiles.forEach(file => {
      if (file.includes('.part')) {
        const partFilePath = path.join(electronCacheDir, file);
        fs.unlinkSync(partFilePath);
        console.log(`   🗑️ 删除不完整文件: ${file}`);
      }
    });
    
    // 确保有完整的缓存文件
    const completeZipPath = path.join(electronCacheDir, 'electron-v36.4.0-win32-x64.zip');
    if (!fs.existsSync(completeZipPath)) {
      fs.copyFileSync(electronZipPath, completeZipPath);
      console.log(`   📋 复制到缓存: electron-v36.4.0-win32-x64.zip`);
    }
    
    console.log('   ✅ 本地Electron压缩包准备完成');

    // 3. 使用时间戳创建唯一的输出目录
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const outputDir = `dist-electron-${timestamp}`;
    
    console.log(`📦 开始构建Electron应用到 ${outputDir}...`);
    
    // 4. 构建配置
    const config = {
      appId: 'com.shop.desktop',
      productName: 'Shop Desktop',
      directories: {
        output: outputDir
      },
      electronDownload: {
        cache: './electron-cache'
      },
      asar: true,
      files: [
        'dist/**/*',
        'electron/**/*',
        'server/**/*',
        '!server/node_modules',
        '!server/logs/**/*',
        '!server/test/**/*',
        '!**/*.map',
        '!**/*.md',
        '!**/README*',
        '!**/CHANGELOG*',
        '!**/LICENSE*',
        '!**/.git*',
        '!**/test/**/*',
        '!**/tests/**/*',
        '!**/__tests__/**/*',
        '!**/spec/**/*',
        '!**/*.spec.*',
        '!**/*.test.*',
        '!**/coverage/**/*',
        '!**/docs/**/*',
        '!**/examples/**/*',
        '!**/demo/**/*'
      ],
      extraResources: [
        {
          from: 'server/db/schema.sql',
          to: 'server/db/schema.sql'
        },
        {
          from: 'server/db/initial-data.sql',
          to: 'server/db/initial-data.sql'
        }
      ],
      win: {
        target: [
          {
            target: 'nsis',
            arch: ['x64']
          },
          {
            target: 'portable',
            arch: ['x64']
          }
        ],
        verifyUpdateCodeSignature: false
      },
      nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true,
        allowElevation: true,
        createDesktopShortcut: true,
        createStartMenuShortcut: true,
        menuCategory: 'Business',
        shortcutName: 'Shop Desktop',
        uninstallDisplayName: 'Shop Desktop',
        differentialPackage: false,
        perMachine: false,
        runAfterFinish: true,
        deleteAppDataOnUninstall: false,

        artifactName: '${productName}-${version}-Setup.${ext}'
      },
      portable: {
        artifactName: '${productName}-${version}-Portable.${ext}'
      },
      nodeGypRebuild: false,
      buildDependenciesFromSource: false,
      compression: 'maximum',
      npmRebuild: false
    };
    
    // 执行构建
    const result = await build({
      config,
      win: ['nsis', 'portable'],
      publish: 'never'
    });

    console.log('\n✅ 全新Electron构建完成！');
    
    // 显示构建结果
    const fullOutputDir = path.resolve(outputDir);
    console.log(`\n📦 安装程序位置: ${fullOutputDir}`);
    
    if (fs.existsSync(fullOutputDir)) {
      const files = fs.readdirSync(fullOutputDir);
      console.log('\n📁 生成的文件:');
      files.forEach(file => {
        const filePath = path.join(fullOutputDir, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`   ${file} (${sizeInMB} MB)`);
      });
    }
    
    console.log('\n🎯 优化说明:');
    console.log('   ✅ 使用本地Electron压缩包，跳过网络下载');
    console.log('   ✅ 避免文件占用问题，使用新的输出目录');
    console.log('   ✅ 生成NSIS安装程序和便携版');
    console.log('   ✅ 移除图标配置，避免格式问题');
    
    return result;
  } catch (error) {
    console.error('❌ 构建失败:', error);
    throw error;
  }
}

// 运行构建
if (require.main === module) {
  buildFresh()
    .then(() => {
      console.log('\n🎉 全新Electron构建成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 构建失败:', error.message);
      process.exit(1);
    });
}

module.exports = buildFresh;
