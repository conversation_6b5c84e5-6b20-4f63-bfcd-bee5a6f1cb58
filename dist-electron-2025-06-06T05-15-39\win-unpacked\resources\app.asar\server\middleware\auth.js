const jwt = require('jsonwebtoken');

// JWT密钥，实际项目中应放在环境变量
const JWT_SECRET = 'your-jwt-secret-key';

// 验证JWT令牌
function verifyToken(req, res, next) {
  // 获取token
  const authHeader = req.headers.authorization;
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json({ 
      success: false, 
      message: '未提供认证令牌' 
    });
  }
  
  // 验证token
  jwt.verify(token, JWT_SECRET, (err, decoded) => {
    if (err) {
      return res.status(401).json({ 
        success: false, 
        message: '认证令牌无效或已过期' 
      });
    }
    
    // 将解码后的用户信息添加到请求对象
    req.user = decoded;
    next();
  });
}

// 检查是否为管理员
function isAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '未认证用户'
    });
  }

  if (req.user.role !== 'admin') {
    return res.status(403).json({
      success: false,
      message: '需要管理员权限'
    });
  }
  next();
}

// 检查是否为收银员或管理员
function isCashierOrAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      success: false,
      message: '未认证用户'
    });
  }

  if (req.user.role !== 'admin' && req.user.role !== 'cashier') {
    return res.status(403).json({
      success: false,
      message: '权限不足'
    });
  }
  next();
}

module.exports = {
  verifyToken,
  isAdmin,
  isCashierOrAdmin
}; 