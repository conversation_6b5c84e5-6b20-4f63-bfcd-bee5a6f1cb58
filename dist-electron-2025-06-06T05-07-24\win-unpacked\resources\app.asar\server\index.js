const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');

// 导入路由
const authRoutes = require('./routes/auth');
const productsRoutes = require('./routes/products');
const categoriesRoutes = require('./routes/categories');
const ordersRoutes = require('./routes/orders');
const reportsRoutes = require('./routes/reports');

// 导入中间件
const { verifyToken, isAdmin, isCashierOrAdmin } = require('./middleware/auth');
const { requestLogger } = require('./middleware/logger');

// 导入数据库初始化
const { initDatabase } = require('./db/init');

const app = express();
const PORT = process.env.PORT || 3000;

// 重定向控制台日志到文件
const logDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

const accessLogStream = fs.createWriteStream(
  path.join(logDir, 'access.log'),
  { flags: 'a' }
);

// 仅将API访问日志写入文件
const originalConsoleLog = console.log;
console.log = function() {
  // 判断是否是API请求日志（简单判断是否包含HTTP方法和状态码）
  const isApiLog = arguments[0] && 
    typeof arguments[0] === 'string' && 
    /^(GET|POST|PUT|DELETE|PATCH).*\[\d{3}\]/.test(arguments[0]);
  
  // 写入日志文件
  if (isApiLog) {
    const timestamp = new Date().toISOString();
    accessLogStream.write(`[${timestamp}] ${arguments[0]}\n`);
  }

  // 仍然输出到控制台
  originalConsoleLog.apply(console, arguments);
};

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 请求日志中间件 - 记录所有API请求
app.use('/api', requestLogger);

// 静态文件服务
app.use(express.static(path.join(__dirname, '../dist')));

// 认证路由 - 无需认证
app.use('/api/auth', authRoutes);

// 需要认证的API路由
// 商品管理 - 管理员可以增删改，所有人可以查看
app.use('/api/products', verifyToken);
app.use('/api/products', productsRoutes);

// 品类管理 - 管理员可以增删改，所有人可以查看
app.use('/api/categories', verifyToken);
app.use('/api/categories', categoriesRoutes);

// 订单管理 - 收银员和管理员都可以操作
app.use('/api/orders', verifyToken);
app.use('/api/orders', ordersRoutes);

// 报表管理 - 仅管理员可以访问
app.use('/api/reports', verifyToken);
app.use('/api/reports', reportsRoutes);

// 所有其他GET请求返回前端应用
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

// 初始化数据库并启动服务器
initDatabase()
  .then(() => {
    app.listen(PORT, () => {
      console.log(`服务器运行在 http://localhost:${PORT}`);
    });
  })
  .catch((err) => {
    console.error('数据库初始化失败:', err);
    process.exit(1);
  }); 