import { useState, useEffect } from "react";
import { FileDown, Calendar as CalendarIcon } from "lucide-react";
import { format, subDays } from "date-fns";
import { toast } from "sonner";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";

import { useOrdersStore } from "@/store/orders";
import api from "@/lib/api";
import { formatCurrency } from "@/lib/utils";

interface SalesData {
  totalSales: number;
  orderCount: number;
  averageOrderValue: number;
  topProducts: {
    productName: string;
    quantity: number;
    amount: number;
    percentage: number;
  }[];
}

interface TrendData {
  name: string;
  totalSales: number;
  orderCount: number;
  averageOrderValue: number;
}

export default function AnalysisPage() {
  const { exportOrders } = useOrdersStore();
  
  // 日期选择状态
  const [startDate, setStartDate] = useState<Date | undefined>(subDays(new Date(), 7));
  const [endDate, setEndDate] = useState<Date | undefined>(new Date());
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);
  



  // 销售数据
  const [salesData, setSalesData] = useState<SalesData>({
    totalSales: 0,
    orderCount: 0,
    averageOrderValue: 0,
    topProducts: [],
  });
  
  // 趋势数据
  const [trendData, setTrendData] = useState<TrendData[]>([]);
  
  const [isLoading, setIsLoading] = useState(false);
  
  // 获取销售数据
  useEffect(() => {
    fetchSalesData();
    fetchTrendData();
  }, [startDate, endDate]);
  
  const fetchSalesData = async () => {
    if (!startDate || !endDate) return;
    
    try {
      setIsLoading(true);
      
      // 格式化日期为YYYY-MM-DD
      const startDateStr = format(startDate, "yyyy-MM-dd");
      const endDateStr = format(endDate, "yyyy-MM-dd");
      
      // 获取销售统计数据
      const response = await api.get(`/reports/sales?startDate=${startDateStr}&endDate=${endDateStr}`);
      
      // 如果接口不存在，使用模拟数据
      if (!response.data || response.status !== 200) {
        // 模拟数据
        setSalesData({
          totalSales: 12580.50,
          orderCount: 156,
          averageOrderValue: 80.64,
          topProducts: [
            { productName: "牛肉面", quantity: 120, amount: 3850, percentage: 48 },
            { productName: "鸡汤面", quantity: 100, amount: 2640, percentage: 33 },
            { productName: "水饺", quantity: 80, amount: 1520, percentage: 19 },
          ]
        });
      } else {
        setSalesData(response.data.data);
      }
    } catch (error) {
      console.error("获取销售数据失败:", error);
      // 使用模拟数据
      setSalesData({
        totalSales: 12580.50,
        orderCount: 156,
        averageOrderValue: 80.64,
        topProducts: [
          { productName: "牛肉面", quantity: 120, amount: 3850, percentage: 48 },
          { productName: "鸡汤面", quantity: 100, amount: 2640, percentage: 33 },
          { productName: "水饺", quantity: 80, amount: 1520, percentage: 19 },
        ]
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  // 获取趋势数据
  const fetchTrendData = async () => {
    if (!startDate || !endDate) return;
    
    try {
      setIsLoading(true);
      
      // 格式化日期为YYYY-MM-DD
      const startDateStr = format(startDate, "yyyy-MM-dd");
      const endDateStr = format(endDate, "yyyy-MM-dd");
      
      // 获取销售趋势数据
      const response = await api.get(`/reports/trends?startDate=${startDateStr}&endDate=${endDateStr}`);
      
      if (response.data && response.status === 200) {
        // 将API返回的数据转换为符合recharts要求的格式
        const formattedData: TrendData[] = response.data.data.map((item: any) => ({
          name: item.date,
          totalSales: Number(item.totalSales || 0),
          orderCount: Number(item.orderCount || 0),
          averageOrderValue: Number(item.averageOrderValue || 0).toFixed(2)
        }));
        setTrendData(formattedData);
      } else {
        setTrendData([]);
        toast.error("获取销售趋势数据失败");
      }
    } catch (error) {
      console.error("获取趋势数据失败:", error);
      setTrendData([]);
      toast.error("获取销售趋势数据失败");
    } finally {
      setIsLoading(false);
    }
  };
  
  // 处理导出账单
  const handleExportOrders = async () => {
    if (!startDate || !endDate) {
      toast.error("请选择开始和结束日期");
      return;
    }
    
    if (startDate > endDate) {
      toast.error("开始日期不能晚于结束日期");
      return;
    }
    
    try {
      await exportOrders(
        format(startDate, "yyyy-MM-dd"),
        format(endDate, "yyyy-MM-dd")
      );
    } catch (error) {
      console.error("导出账单失败:", error);
      toast.error("导出账单失败");
    }
  };
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2">
        <h2 className="text-3xl font-bold tracking-tight">账单分析</h2>
        <p className="text-muted-foreground">
          查看销售数据和导出账单报表
        </p>
      </div>
      
      {/* 日期选择和导出按钮 */}
      <div className="flex flex-col items-start gap-4 sm:flex-row sm:items-center">
        <div className="grid gap-2">
          <Label>开始日期</Label>
          <Popover open={startDateOpen} onOpenChange={setStartDateOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-[200px] justify-start text-left"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {startDate ? format(startDate, "yyyy-MM-dd") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={startDate}
                onSelect={(date) => {
                  setStartDate(date);
                  setStartDateOpen(false);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <div className="grid gap-2">
          <Label>结束日期</Label>
          <Popover open={endDateOpen} onOpenChange={setEndDateOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-[200px] justify-start text-left"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? format(endDate, "yyyy-MM-dd") : "选择日期"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={(date) => {
                  setEndDate(date);
                  setEndDateOpen(false);
                }}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <Button 
          className="ml-auto" 
          onClick={handleExportOrders}
          disabled={isLoading}
        >
          <FileDown className="mr-2 h-4 w-4" /> 导出账单
        </Button>
      </div>

      {isLoading ? (
        <div className="flex h-64 items-center justify-center">
          <p>加载数据中...</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">总销售额</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(salesData.totalSales)}</div>
                <p className="text-xs text-muted-foreground">
                  所选时间段内的总销售金额
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">订单数量</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{salesData.orderCount}</div>
                <p className="text-xs text-muted-foreground">
                  所选时间段内的订单总数
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">平均客单价</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(salesData.averageOrderValue)}</div>
                <p className="text-xs text-muted-foreground">
                  所选时间段内的平均订单金额
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">畅销商品</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {salesData.topProducts?.length > 0 ? salesData.topProducts[0].productName : "-"}
                </div>
                <p className="text-xs text-muted-foreground">
                  所选时间段内销量最高的商品
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>销售趋势</CardTitle>
            </CardHeader>
            <CardContent className="h-80">
              {trendData.length === 0 ? (
                <div className="flex h-full items-center justify-center">
                  <p className="text-muted-foreground">
                    暂无销售趋势数据
                  </p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={trendData}
                    margin={{
                      top: 5,
                      right: 30,
                      left: 20,
                      bottom: 5,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => {
                      if (name === "totalSales" || name === "averageOrderValue") {
                        return [formatCurrency(Number(value)), name === "totalSales" ? "销售额" : "客单价"];
                      }
                      return [value, name === "orderCount" ? "订单数" : name];
                    }} />
                    <Legend 
                      payload={[
                        { value: '销售额', type: 'line', color: '#8884d8' },
                        { value: '订单数', type: 'line', color: '#82ca9d' },
                        { value: '客单价', type: 'line', color: '#ff7300' }
                      ]} 
                    />
                    <Line type="monotone" dataKey="totalSales" stroke="#8884d8" activeDot={{ r: 8 }} name="销售额" />
                    <Line type="monotone" dataKey="orderCount" stroke="#82ca9d" name="订单数" />
                    <Line type="monotone" dataKey="averageOrderValue" stroke="#ff7300" activeDot={{ r: 8 }} name="客单价" />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>商品销售排行</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {salesData.topProducts?.map((product, index) => (
                  <div key={index} className="flex items-center">
                    <div className="w-full max-w-md">
                      <div className="flex items-center justify-between">
                        <span>{product.productName}</span>
                        <span>{formatCurrency(product.amount)} ({product.percentage}%)</span>
                      </div>
                      <div className="mt-1 h-2 w-full overflow-hidden rounded-full bg-muted">
                        <div 
                          className="h-full bg-primary" 
                          style={{ width: `${product.percentage}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
} 