const { build } = require('electron-builder');
const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

async function buildLocalSimple() {
  try {
    console.log('🚀 开始简化本地Electron构建...\n');
    
    // 1. 清理构建目录
    console.log('📁 清理构建目录...');
    const distElectronPath = path.join(__dirname, 'dist-electron');
    if (fs.existsSync(distElectronPath)) {
      try {
        // 尝试多次删除，处理文件占用问题
        let retries = 3;
        while (retries > 0) {
          try {
            fs.rmSync(distElectronPath, { recursive: true, force: true });
            break;
          } catch (error) {
            if (error.code === 'EBUSY' && retries > 1) {
              console.log(`   ⏳ 文件被占用，等待2秒后重试... (剩余${retries-1}次)`);
              await new Promise(resolve => setTimeout(resolve, 2000));
              retries--;
            } else {
              throw error;
            }
          }
        }
      } catch (error) {
        console.log(`   ⚠️ 无法完全清理构建目录: ${error.message}`);
        console.log('   💡 请关闭所有Electron应用后重试');
        throw error;
      }
    }
    console.log('   ✅ 构建目录清理完成');

    // 2. 构建前端
    console.log('🔨 构建React前端...');
    execSync('pnpm run build', { stdio: 'inherit' });
    console.log('   ✅ 前端构建完成');

    // 3. 检查并准备本地Electron压缩包
    console.log('📦 准备本地Electron压缩包...');
    const electronZipPath = path.join(__dirname, 'electron-v36.4.0-win32-x64.zip');
    if (!fs.existsSync(electronZipPath)) {
      throw new Error(`本地Electron压缩包不存在: ${electronZipPath}`);
    }
    
    // 确保electron-cache目录存在
    const electronCacheDir = path.join(__dirname, 'electron-cache');
    if (!fs.existsSync(electronCacheDir)) {
      fs.mkdirSync(electronCacheDir, { recursive: true });
    }
    
    // 检查现有缓存文件
    const existingCacheFiles = fs.readdirSync(electronCacheDir);
    console.log(`   📋 现有缓存文件: ${existingCacheFiles.join(', ')}`);

    // 清理不完整的下载文件
    existingCacheFiles.forEach(file => {
      if (file.includes('.part')) {
        const partFilePath = path.join(electronCacheDir, file);
        fs.unlinkSync(partFilePath);
        console.log(`   🗑️ 删除不完整文件: ${file}`);
      }
    });

    // 确保有完整的缓存文件
    const completeZipPath = path.join(electronCacheDir, 'electron-v36.4.0-win32-x64.zip');
    if (!fs.existsSync(completeZipPath)) {
      fs.copyFileSync(electronZipPath, completeZipPath);
      console.log(`   📋 复制到缓存: electron-v36.4.0-win32-x64.zip`);
    }
    
    console.log('   ✅ 本地Electron压缩包准备完成');

    // 4. 使用现有的package.json配置进行构建
    console.log('📦 开始构建Electron应用（使用本地缓存）...');
    
    // 执行构建 - 使用现有配置
    const result = await build({
      win: ['nsis', 'portable'],
      publish: 'never'
    });

    console.log('\n✅ 本地Electron构建完成！');
    
    // 显示构建结果
    const outputDir = path.resolve('dist-electron');
    console.log(`\n📦 安装程序位置: ${outputDir}`);
    
    if (fs.existsSync(outputDir)) {
      const files = fs.readdirSync(outputDir);
      console.log('\n📁 生成的文件:');
      files.forEach(file => {
        const filePath = path.join(outputDir, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`   ${file} (${sizeInMB} MB)`);
      });
    }
    
    console.log('\n🎯 优化说明:');
    console.log('   ✅ 使用本地Electron压缩包，跳过网络下载');
    console.log('   ✅ 保持现有配置，确保兼容性');
    console.log('   ✅ 生成NSIS安装程序和便携版');
    
    return result;
  } catch (error) {
    console.error('❌ 构建失败:', error);
    throw error;
  }
}

// 运行构建
if (require.main === module) {
  buildLocalSimple()
    .then(() => {
      console.log('\n🎉 简化本地Electron构建成功完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 构建失败:', error.message);
      process.exit(1);
    });
}

module.exports = buildLocalSimple;
