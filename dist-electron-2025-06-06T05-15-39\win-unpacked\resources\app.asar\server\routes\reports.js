const express = require('express');
const router = express.Router();
const { isAdmin } = require('../middleware/auth');

// Get database connection - works in both standalone and Electron environments
function getDb() {
  if (global.getDb) {
    // Electron environment
    return global.getDb();
  } else {
    // Standalone server environment
    const { getDb: localGetDb } = require('../db/init');
    return localGetDb();
  }
}

// 获取销售报表数据 - 仅管理员可访问
router.get('/sales', isAdmin, (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: startDate, endDate'
      });
    }
    
    const db = getDb();
    
    // 获取总销售额和订单数量
    const salesData = db.prepare(`
      SELECT 
        COUNT(*) as orderCount,
        SUM(total_amount) as totalSales
      FROM orders
      WHERE DATE(created_at) BETWEEN DATE(?) AND DATE(?)
      AND status = 'completed'
    `).get(startDate, endDate);
    
    // 计算平均客单价
    const averageOrderValue = salesData.orderCount > 0
      ? salesData.totalSales / salesData.orderCount
      : 0;
    
    // 获取热销商品数据
    const topProducts = db.prepare(`
      SELECT 
        oi.product_name as productName,
        SUM(oi.quantity) as quantity,
        SUM(oi.price * oi.quantity) as amount
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      WHERE DATE(o.created_at) BETWEEN DATE(?) AND DATE(?)
      AND o.status = 'completed'
      GROUP BY oi.product_name
      ORDER BY amount DESC
      LIMIT 5
    `).all(startDate, endDate);
    
    // 计算销售百分比
    const totalAmount = topProducts.reduce((sum, item) => sum + item.amount, 0);
    const topProductsWithPercentage = topProducts.map(product => ({
      ...product,
      percentage: Math.round((product.amount / totalAmount) * 100)
    }));
    
    res.json({
      success: true,
      data: {
        totalSales: salesData.totalSales || 0,
        orderCount: salesData.orderCount || 0,
        averageOrderValue,
        topProducts: topProductsWithPercentage
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取销售报表失败'
    });
  }
});

// 获取销售趋势数据 - 仅管理员可访问
router.get('/trends', isAdmin, (req, res) => {
  try {
    const { startDate, endDate } = req.query;
    
    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数: startDate, endDate'
      });
    }
    
    const db = getDb();
    
    // 获取每日销售数据
    const trends = db.prepare(`
      SELECT 
        DATE(created_at) as date,
        COUNT(*) as orderCount,
        SUM(total_amount) as totalSales
      FROM orders
      WHERE DATE(created_at) BETWEEN DATE(?) AND DATE(?)
      AND status = 'completed'
      GROUP BY DATE(created_at)
      ORDER BY date ASC
    `).all(startDate, endDate);
    
    // 格式化日期并计算平均客单价
    const formattedTrends = trends.map(day => ({
      date: day.date,
      totalSales: day.totalSales || 0,
      orderCount: day.orderCount || 0,
      averageOrderValue: day.orderCount > 0 ? day.totalSales / day.orderCount : 0
    }));
    
    res.json({
      success: true,
      data: formattedTrends
    });
  } catch (error) {
    console.error('获取销售趋势数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取销售趋势数据失败'
    });
  }
});

module.exports = router; 